<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>今天穿什么 - UI 预览</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 480px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .app-title {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            padding: 8px 0;
            line-height: 1.3;
            color: #667eea;
            text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }

        .page-subtitle {
            text-align: center;
            font-size: 16px;
            color: #666;
            margin-bottom: 32px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            background: #fff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .btn {
            width: 100%;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .switch-page {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e1e5e9;
            color: #666;
        }

        .link-btn {
            background: none;
            border: none;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .link-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .preview-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .preview-controls button {
            margin: 4px;
            padding: 8px 16px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .preview-controls button.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="preview-controls">
        <div style="margin-bottom: 8px; font-weight: 600; color: #333;">UI 预览</div>
        <button onclick="showLogin()" class="active" id="loginBtn">登录页面</button>
        <button onclick="showRegister()" id="registerBtn">注册页面</button>
    </div>

    <div class="app-container" id="loginPage">
        <div class="auth-container">
            <h1 class="app-title">今天穿什么</h1>
            <p class="page-subtitle">✨ 智能穿搭，从登录开始</p>
            
            <form>
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" placeholder="请输入您的用户名" class="form-input" />
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" placeholder="请输入您的密码" class="form-input" />
                </div>
                
                <button type="button" class="btn btn-primary">开始穿搭之旅 →</button>
            </form>
            
            <div class="switch-page">
                <span>还没有账号？</span>
                <button class="link-btn" onclick="showRegister()">立即注册</button>
            </div>
        </div>
    </div>

    <div class="app-container" id="registerPage" style="display: none;">
        <div class="auth-container">
            <h1 class="app-title">今天穿什么</h1>
            <p class="page-subtitle">🎨 创建账号，开启个性化穿搭</p>
            
            <form>
                <div class="form-group">
                    <label class="form-label">用户名 *</label>
                    <input type="text" placeholder="请输入用户名（3-20个字符）" class="form-input" />
                </div>
                
                <div class="form-group">
                    <label class="form-label">邮箱 *</label>
                    <input type="email" placeholder="请输入邮箱地址" class="form-input" />
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码 *</label>
                    <input type="password" placeholder="请输入密码（至少6个字符）" class="form-input" />
                </div>
                
                <button type="button" class="btn btn-primary">创建账号 🎉</button>
            </form>
            
            <div class="switch-page">
                <span>已有账号？</span>
                <button class="link-btn" onclick="showLogin()">立即登录</button>
            </div>
        </div>
    </div>

    <script>
        function showLogin() {
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('registerPage').style.display = 'none';
            document.getElementById('loginBtn').classList.add('active');
            document.getElementById('registerBtn').classList.remove('active');
        }

        function showRegister() {
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('registerPage').style.display = 'flex';
            document.getElementById('loginBtn').classList.remove('active');
            document.getElementById('registerBtn').classList.add('active');
        }
    </script>
</body>
</html>
