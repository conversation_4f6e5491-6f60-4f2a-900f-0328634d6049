<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息表单优化预览</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title::before {
            content: "";
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        /* 两列布局 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 8px;
            align-items: start;
        }

        /* 特殊输入类型样式 */
        .form-group-special {
            margin-bottom: 20px;
            position: relative;
        }

        .form-label-special {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: #667eea;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-input-special {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 15px;
            font-weight: 400;
            background: #fff;
            transition: all 0.3s ease;
            outline: none;
            color: #333;
        }

        .form-input-special:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-input-special:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 下拉框特殊样式 */
        .form-input-special[type="date"] {
            color-scheme: light;
        }

        .form-input-special[type="date"]::-webkit-calendar-picker-indicator {
            background-color: #667eea;
            border-radius: 3px;
            cursor: pointer;
        }

        /* 数字输入框样式 */
        .form-input-special[type="number"] {
            -moz-appearance: textfield;
        }

        .form-input-special[type="number"]::-webkit-outer-spin-button,
        .form-input-special[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* 对比区域 */
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 40px;
        }

        .comparison-section {
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 16px;
            background: #f8f9fa;
        }

        .comparison-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }

        .optimized {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
        }

        .optimized .comparison-title {
            color: #28a745;
        }

        .optimized .comparison-title::after {
            content: " ✨";
        }

        .old-style {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #fff0f0 100%);
        }

        .old-style .comparison-title {
            color: #dc3545;
        }

        /* 旧样式模拟 */
        .form-input-old {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            background: #fff;
            transition: all 0.3s ease;
            outline: none;
            margin-bottom: 20px;
        }

        .form-label-old {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 24px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #667eea; margin-bottom: 30px; font-size: 24px;">个人信息表单优化</h1>
        
        <!-- 优化后的表单 -->
        <div class="section-title">个人信息（可选）</div>
        
        <div class="form-row">
            <div class="form-group-special">
                <label class="form-label-special">性别</label>
                <select class="form-input-special">
                    <option value="">请选择</option>
                    <option value="male">男</option>
                    <option value="female">女</option>
                    <option value="other">其他</option>
                </select>
            </div>

            <div class="form-group-special">
                <label class="form-label-special">生日</label>
                <input type="date" class="form-input-special" value="1990-01-01">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group-special">
                <label class="form-label-special">身高 (cm)</label>
                <input type="number" class="form-input-special" placeholder="如：170" min="100" max="250">
            </div>

            <div class="form-group-special">
                <label class="form-label-special">体重 (kg)</label>
                <input type="number" class="form-input-special" placeholder="如：65" min="30" max="200">
            </div>
        </div>

        <!-- 对比区域 -->
        <div class="comparison">
            <div class="comparison-section optimized">
                <div class="comparison-title">优化后</div>
                <div style="font-size: 14px; color: #28a745;">
                    ✅ 紧凑的标签设计<br>
                    ✅ 统一的视觉风格<br>
                    ✅ 更好的间距控制<br>
                    ✅ 响应式布局<br>
                    ✅ 无重叠问题
                </div>
            </div>

            <div class="comparison-section old-style">
                <div class="comparison-title">优化前</div>
                <div style="font-size: 14px; color: #dc3545;">
                    ❌ 标签样式不统一<br>
                    ❌ 间距控制不佳<br>
                    ❌ 可能出现重叠<br>
                    ❌ 视觉层次混乱<br>
                    ❌ 移动端体验差
                </div>
            </div>
        </div>
    </div>
</body>
</html>
