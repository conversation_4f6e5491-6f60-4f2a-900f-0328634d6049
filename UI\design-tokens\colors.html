<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>What to Wear - 颜色系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .title {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 18px;
            color: #64748b;
            margin-bottom: 24px;
        }

        .nav {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 40px;
        }

        .nav-link {
            padding: 8px 16px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            text-decoration: none;
            color: #64748b;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .section {
            background: white;
            border-radius: 16px;
            border: 1px solid #e2e8f0;
            padding: 32px;
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }

        .section-description {
            color: #64748b;
            margin-bottom: 32px;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .color-card {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }

        .color-preview {
            height: 120px;
            position: relative;
        }

        .color-info {
            padding: 16px;
        }

        .color-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .color-values {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .color-value {
            font-size: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            color: #64748b;
            background: #f8fafc;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .usage-example {
            margin-top: 12px;
            font-size: 12px;
            color: #64748b;
        }

        .palette-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }

        .palette-item {
            text-align: center;
        }

        .palette-color {
            height: 60px;
            border-radius: 8px;
            margin-bottom: 8px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .palette-label {
            font-size: 11px;
            color: #64748b;
            font-weight: 500;
        }

        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .color-card:hover .copy-btn {
            opacity: 1;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin-bottom: 24px;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../index.html" class="back-link">
            <span>←</span>
            <span>返回 UI 预览中心</span>
        </a>

        <header class="header">
            <h1 class="title">颜色系统</h1>
            <p class="subtitle">What to Wear 项目的完整配色方案</p>
        </header>

        <nav class="nav">
            <a href="#primary" class="nav-link active">主色调</a>
            <a href="#neutral" class="nav-link">中性色</a>
            <a href="#semantic" class="nav-link">语义色</a>
            <a href="#gradients" class="nav-link">渐变色</a>
        </nav>

        <section class="section" id="primary">
            <h2 class="section-title">主色调</h2>
            <p class="section-description">品牌主色调，用于按钮、链接、强调元素等</p>
            
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-preview" style="background: #667eea;">
                        <button class="copy-btn" onclick="copyColor('#667eea')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Primary Blue</div>
                        <div class="color-values">
                            <div class="color-value">HEX: #667eea</div>
                            <div class="color-value">RGB: 102, 126, 234</div>
                            <div class="color-value">HSL: 230, 75%, 66%</div>
                        </div>
                        <div class="usage-example">用于：主要按钮、链接、品牌元素</div>
                    </div>
                </div>

                <div class="color-card">
                    <div class="color-preview" style="background: #764ba2;">
                        <button class="copy-btn" onclick="copyColor('#764ba2')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Secondary Purple</div>
                        <div class="color-values">
                            <div class="color-value">HEX: #764ba2</div>
                            <div class="color-value">RGB: 118, 75, 162</div>
                            <div class="color-value">HSL: 270, 37%, 46%</div>
                        </div>
                        <div class="usage-example">用于：渐变、装饰元素</div>
                    </div>
                </div>
            </div>

            <h3 style="margin: 32px 0 16px 0; font-size: 18px;">主色调色板</h3>
            <div class="palette-grid">
                <div class="palette-item">
                    <div class="palette-color" style="background: #4c51bf;"></div>
                    <div class="palette-label">900</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #5a67d8;"></div>
                    <div class="palette-label">700</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #667eea;"></div>
                    <div class="palette-label">500</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #7c3aed;"></div>
                    <div class="palette-label">300</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #a78bfa;"></div>
                    <div class="palette-label">200</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #c4b5fd;"></div>
                    <div class="palette-label">100</div>
                </div>
            </div>
        </section>

        <section class="section" id="neutral">
            <h2 class="section-title">中性色</h2>
            <p class="section-description">用于文本、背景、边框等基础元素</p>
            
            <div class="palette-grid">
                <div class="palette-item">
                    <div class="palette-color" style="background: #0f172a;"></div>
                    <div class="palette-label">900</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #1e293b;"></div>
                    <div class="palette-label">800</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #334155;"></div>
                    <div class="palette-label">700</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #475569;"></div>
                    <div class="palette-label">600</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #64748b;"></div>
                    <div class="palette-label">500</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #94a3b8;"></div>
                    <div class="palette-label">400</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #cbd5e1;"></div>
                    <div class="palette-label">300</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #e2e8f0;"></div>
                    <div class="palette-label">200</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #f1f5f9;"></div>
                    <div class="palette-label">100</div>
                </div>
                <div class="palette-item">
                    <div class="palette-color" style="background: #f8fafc;"></div>
                    <div class="palette-label">50</div>
                </div>
            </div>
        </section>

        <section class="section" id="semantic">
            <h2 class="section-title">语义色</h2>
            <p class="section-description">用于表示状态、反馈和功能的颜色</p>
            
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-preview" style="background: #10b981;">
                        <button class="copy-btn" onclick="copyColor('#10b981')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Success Green</div>
                        <div class="color-values">
                            <div class="color-value">HEX: #10b981</div>
                        </div>
                        <div class="usage-example">成功状态、确认操作</div>
                    </div>
                </div>

                <div class="color-card">
                    <div class="color-preview" style="background: #f59e0b;">
                        <button class="copy-btn" onclick="copyColor('#f59e0b')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Warning Orange</div>
                        <div class="color-values">
                            <div class="color-value">HEX: #f59e0b</div>
                        </div>
                        <div class="usage-example">警告信息、注意事项</div>
                    </div>
                </div>

                <div class="color-card">
                    <div class="color-preview" style="background: #ef4444;">
                        <button class="copy-btn" onclick="copyColor('#ef4444')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Error Red</div>
                        <div class="color-values">
                            <div class="color-value">HEX: #ef4444</div>
                        </div>
                        <div class="usage-example">错误状态、删除操作</div>
                    </div>
                </div>

                <div class="color-card">
                    <div class="color-preview" style="background: #3b82f6;">
                        <button class="copy-btn" onclick="copyColor('#3b82f6')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Info Blue</div>
                        <div class="color-values">
                            <div class="color-value">HEX: #3b82f6</div>
                        </div>
                        <div class="usage-example">信息提示、帮助文本</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="gradients">
            <h2 class="section-title">渐变色</h2>
            <p class="section-description">用于背景、装饰和特殊效果的渐变</p>
            
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-preview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <button class="copy-btn" onclick="copyGradient('linear-gradient(135deg, #667eea 0%, #764ba2 100%)')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Primary Gradient</div>
                        <div class="color-values">
                            <div class="color-value">135deg, #667eea → #764ba2</div>
                        </div>
                        <div class="usage-example">主要背景、按钮渐变</div>
                    </div>
                </div>

                <div class="color-card">
                    <div class="color-preview" style="background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);">
                        <button class="copy-btn" onclick="copyGradient('linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)')">复制</button>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Light Gradient</div>
                        <div class="color-values">
                            <div class="color-value">135deg, #f8f9ff → #f0f4ff</div>
                        </div>
                        <div class="usage-example">卡片背景、微妙装饰</div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        function copyColor(color) {
            navigator.clipboard.writeText(color).then(() => {
                alert(`已复制颜色: ${color}`);
            });
        }

        function copyGradient(gradient) {
            navigator.clipboard.writeText(gradient).then(() => {
                alert(`已复制渐变: ${gradient}`);
            });
        }

        // 平滑滚动到锚点
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                    
                    // 更新激活状态
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
