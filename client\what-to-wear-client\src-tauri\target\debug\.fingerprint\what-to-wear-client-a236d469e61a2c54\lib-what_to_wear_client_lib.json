{"rustc": 524190467255570058, "features": "[]", "declared_features": "[]", "target": 16723234862926345532, "profile": 11597332650809196192, "path": 17777289886553719987, "deps": [[986654470566463325, "serde_json", false, 11703646689729118526], [8540126170111459554, "tauri", false, 18368566557703743042], [10633404241517405153, "serde", false, 7301605132656744135], [15765980575016491720, "build_script_build", false, 6787356555186588358], [17419507442675575862, "tauri_plugin_opener", false, 15477366573974582972]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\what-to-wear-client-a236d469e61a2c54\\dep-lib-what_to_wear_client_lib", "checksum": false}}], "rustflags": [], "metadata": 18393520624215946259, "config": 2202906307356721367, "compile_kind": 0}