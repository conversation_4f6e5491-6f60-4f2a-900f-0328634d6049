<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮动标签设计预览</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .preview-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 30px;
        }

        .design-option {
            padding: 30px;
            border-radius: 16px;
            border: 2px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .design-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .option-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        /* 浮动标签样式 */
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 20px 20px 12px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            background: #fff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-input:focus + .form-label,
        .form-input:not(:placeholder-shown) + .form-label {
            transform: translateY(-8px) scale(0.85);
            color: #667eea;
            background: white;
            padding: 0 8px;
        }

        .form-label {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            font-weight: 400;
            color: #999;
            pointer-events: none;
            transition: all 0.3s ease;
            background: transparent;
            z-index: 1;
        }

        /* 传统标签样式 */
        .form-label-traditional {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #555;
            margin-bottom: 8px;
        }

        .form-input-traditional {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            background: #fff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input-traditional:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .pros-cons {
            margin-top: 20px;
            font-size: 14px;
        }

        .pros {
            color: #28a745;
            margin-bottom: 10px;
        }

        .cons {
            color: #dc3545;
        }

        .recommended {
            border-color: #28a745 !important;
            background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
        }

        .recommended .option-title::after {
            content: " ✨ 推荐";
            color: #28a745;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="preview-section">
            <h1 class="section-title">标签位置设计对比</h1>
            
            <div class="comparison">
                <!-- 浮动标签设计 -->
                <div class="design-option recommended">
                    <h3 class="option-title">浮动标签设计</h3>
                    
                    <div class="form-group">
                        <div class="form-input-wrapper">
                            <input type="text" class="form-input" placeholder=" " id="floating-username">
                            <label class="form-label" for="floating-username">用户名</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-input-wrapper">
                            <input type="password" class="form-input" placeholder=" " id="floating-password">
                            <label class="form-label" for="floating-password">密码</label>
                        </div>
                    </div>
                    
                    <div class="pros-cons">
                        <div class="pros">
                            ✅ 优点：<br>
                            • 节省垂直空间<br>
                            • 现代化设计趋势<br>
                            • 优雅的动画效果<br>
                            • 提升用户体验<br>
                            • 符合 Material Design
                        </div>
                        <div class="cons">
                            ❌ 缺点：<br>
                            • 实现稍复杂<br>
                            • 需要 JavaScript 支持
                        </div>
                    </div>
                </div>

                <!-- 传统标签设计 -->
                <div class="design-option">
                    <h3 class="option-title">传统标签设计</h3>
                    
                    <div class="form-group">
                        <label class="form-label-traditional">用户名</label>
                        <input type="text" class="form-input-traditional" placeholder="请输入用户名">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label-traditional">密码</label>
                        <input type="password" class="form-input-traditional" placeholder="请输入密码">
                    </div>
                    
                    <div class="pros-cons">
                        <div class="pros">
                            ✅ 优点：<br>
                            • 实现简单<br>
                            • 兼容性好<br>
                            • 清晰明确
                        </div>
                        <div class="cons">
                            ❌ 缺点：<br>
                            • 占用更多空间<br>
                            • 视觉层次感弱<br>
                            • 缺乏现代感<br>
                            • 用户体验一般
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="preview-section">
            <h2 class="section-title">其他设计方案</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="padding: 20px; border: 1px solid #e1e5e9; border-radius: 12px; text-align: center;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">内联标签</h4>
                    <p style="font-size: 14px; color: #666;">标签作为 placeholder 显示在输入框内</p>
                </div>
                
                <div style="padding: 20px; border: 1px solid #e1e5e9; border-radius: 12px; text-align: center;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">左侧标签</h4>
                    <p style="font-size: 14px; color: #666;">标签在输入框左侧，适合表单较宽的情况</p>
                </div>
                
                <div style="padding: 20px; border: 1px solid #e1e5e9; border-radius: 12px; text-align: center;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">图标标签</h4>
                    <p style="font-size: 14px; color: #666;">使用图标代替文字标签，更简洁</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
