<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>What to Wear - 登录页面预览</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: white;
            text-decoration: none;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            z-index: 100;
        }

        .back-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 480px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .app-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 24px;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .app-title {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            padding: 8px 0;
            line-height: 1.3;
            color: #667eea;
            text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }

        .page-subtitle {
            text-align: center;
            font-size: 16px;
            color: #666;
            margin-bottom: 32px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 20px 20px 12px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            background: #fff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-input:focus + .form-label,
        .form-input:not(:placeholder-shown) + .form-label {
            transform: translateY(-8px) scale(0.85);
            color: #667eea;
            background: white;
            padding: 0 8px;
        }

        .form-label {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            font-weight: 400;
            color: #999;
            pointer-events: none;
            transition: all 0.3s ease;
            background: transparent;
            z-index: 1;
        }

        .btn {
            width: 100%;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            outline: none;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .additional-options {
            margin: 24px 0;
            text-align: center;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .switch-page {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e1e5e9;
            color: #666;
        }

        .link-btn {
            background: none;
            border: none;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .link-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .demo-notice {
            background: #f0f4ff;
            border: 1px solid #e8ecf7;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 24px;
            font-size: 14px;
            color: #667eea;
            text-align: center;
        }

        .demo-notice strong {
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .auth-container {
                padding: 24px;
                margin: 10px;
            }
            
            .app-title {
                font-size: 28px;
            }
            
            .back-link {
                position: static;
                margin: 20px;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-link">
        <span>←</span>
        <span>返回 UI 预览中心</span>
    </a>

    <div class="app-container">
        <div class="auth-container">
            <div class="app-logo">
                <div class="logo-icon">👗</div>
            </div>
            
            <h1 class="app-title">What to Wear</h1>
            <p class="page-subtitle">✨ 智能穿搭，从登录开始</p>
            
            <div class="demo-notice">
                <strong>UI 预览模式</strong> - 这是登录页面的设计预览
            </div>
            
            <form>
                <div class="form-group">
                    <div class="form-input-wrapper">
                        <input type="text" placeholder=" " class="form-input" />
                        <label class="form-label">用户名</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-input-wrapper">
                        <input type="password" placeholder=" " class="form-input" />
                        <label class="form-label">密码</label>
                    </div>
                </div>
                
                <div class="additional-options">
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="showLoginSuccess()">
                    开始穿搭之旅 →
                </button>
            </form>
            
            <div class="switch-page">
                <span>还没有账号？</span>
                <a href="register-page.html" class="link-btn">立即注册</a>
            </div>
        </div>
    </div>

    <script>
        function showLoginSuccess() {
            alert('🎉 登录成功！\n\n这是UI预览模式，实际登录功能需要在React应用中实现。');
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });

            // 按钮点击效果
            const button = document.querySelector('.btn-primary');
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
